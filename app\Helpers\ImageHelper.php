<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageHelper
{
    /**
     * Upload and resize image
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param int $maxWidth
     * @param int $maxHeight
     * @return string
     */
    public static function uploadAndResize(UploadedFile $file, string $folder, int $maxWidth = 800, int $maxHeight = 600): string
    {
        // Generate unique filename
        $extension = strtolower($file->getClientOriginalExtension());
        $filename = time() . '_' . uniqid() . '.' . $extension;
        $path = $folder . '/' . $filename;

        // Create directory if not exists
        $fullPath = storage_path('app/public/' . $folder);
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Check if we can process images
        if (!extension_loaded('gd')) {
            // If GD is not available, just store the original file
            \Log::warning('GD extension not available, storing original image without resizing');
            $storedPath = $file->store($folder, 'public');
            return $storedPath;
        }

        // Store original file temporarily
        $tempPath = $file->store('temp', 'public');
        $fullTempPath = storage_path('app/public/' . $tempPath);

        // Resize image if needed
        try {
            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullTempPath);

            // Get current dimensions
            $currentWidth = $image->width();
            $currentHeight = $image->height();

            // Only resize if image is larger than max dimensions
            if ($currentWidth > $maxWidth || $currentHeight > $maxHeight) {
                // Calculate aspect ratio
                $ratio = min($maxWidth / $currentWidth, $maxHeight / $currentHeight);
                $newWidth = (int)($currentWidth * $ratio);
                $newHeight = (int)($currentHeight * $ratio);

                $image->resize($newWidth, $newHeight);
            }

            // Ensure the target directory exists
            $targetDir = dirname(storage_path('app/public/' . $path));
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            // Save processed image
            $image->save(storage_path('app/public/' . $path));

            // Delete temp file
            Storage::disk('public')->delete($tempPath);

            return $path;
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Image processing failed: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
                'error' => $e->getTraceAsString()
            ]);

            // If image processing fails, try to save original file
            try {
                Storage::disk('public')->delete($tempPath);
                return $file->store($folder, 'public');
            } catch (\Exception $fallbackError) {
                \Log::error('Fallback file storage also failed: ' . $fallbackError->getMessage());
                throw new \Exception('Failed to upload image: ' . $e->getMessage());
            }
        }
    }

    /**
     * Delete image file
     *
     * @param string|null $imagePath
     * @return bool
     */
    public static function deleteImage(?string $imagePath): bool
    {
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            return Storage::disk('public')->delete($imagePath);
        }
        return false;
    }

    /**
     * Get image URL
     *
     * @param string|null $imagePath
     * @param string $default
     * @return string
     */
    public static function getImageUrl(?string $imagePath, string $default = '/images/no-image.svg'): string
    {
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            return asset('storage/' . $imagePath);
        }
        return asset($default);
    }

    /**
     * Validate image file
     *
     * @param UploadedFile $file
     * @param int $maxSize (in KB)
     * @return array
     */
    public static function validateImage(UploadedFile $file, int $maxSize = 2048): array
    {
        $errors = [];

        // Check file type
        $allowedTypes = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            $errors[] = 'ไฟล์ต้องเป็นรูปภาพ (jpeg, jpg, png, gif, webp)';
        }

        // Check file size
        if ($file->getSize() > $maxSize * 1024) {
            $errors[] = "ขนาดไฟล์ต้องไม่เกิน {$maxSize} KB";
        }

        // Check if it's actually an image
        try {
            $imageInfo = getimagesize($file->getPathname());
            if (!$imageInfo) {
                $errors[] = 'ไฟล์ไม่ใช่รูปภาพที่ถูกต้อง';
            }
        } catch (\Exception $e) {
            $errors[] = 'ไม่สามารถอ่านไฟล์รูปภาพได้';
        }

        return $errors;
    }

    /**
     * Upload image without resizing (fallback method)
     *
     * @param UploadedFile $file
     * @param string $folder
     * @return string
     */
    public static function uploadImage(UploadedFile $file, string $folder): string
    {
        return self::uploadAndResize($file, $folder);
    }

    /**
     * Create thumbnail
     *
     * @param string $imagePath
     * @param int $width
     * @param int $height
     * @return string|null
     */
    public static function createThumbnail(string $imagePath, int $width = 150, int $height = 150): ?string
    {
        try {
            $fullPath = storage_path('app/public/' . $imagePath);

            if (!file_exists($fullPath)) {
                return null;
            }

            $pathInfo = pathinfo($imagePath);
            $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];
            $thumbnailFullPath = storage_path('app/public/' . $thumbnailPath);

            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullPath);
            $image->cover($width, $height);
            $image->save($thumbnailFullPath);

            return $thumbnailPath;
        } catch (\Exception $e) {
            return null;
        }
    }
}
